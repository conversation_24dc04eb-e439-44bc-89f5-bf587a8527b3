import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic
import QtQuick.Controls as Controls

Popup {
    id: popup
    width: parent.width
    height: parent.height
    modal: true
    focus: true
    closePolicy: Popup.NoAutoClose

    background: Rectangle {
        color: "#393939"
    }

    Button {
        id: closeButton
        text: qsTr("✕")
        font.pixelSize: 16
        width: 40
        height: 40
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 15
        onClicked: {
            popup.close();
        }

        background: Rectangle {
            color: "#4c4c4c"
            radius: 4
        }

        contentItem: Text {
            text: closeButton.text
            color: "white"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            anchors.fill: parent
        }

        ToolTip.text: qsTr("Close")
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        // Add your filter content here
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            color: "#4c4c4c"
            radius: 4

            Text {
                anchors.centerIn: parent
                text: qsTr("Date Filter (Calendar placeholder)")
                color: "white"
                font.pixelSize: 14
            }
        }
    }
}
